SELECT
	sub.`Emp`,
	sub.`CC $`,
	sub.`New $`,
	sub.`New $ (Invoice)`,
	sub.`Paid Invoice $`,
	sub.`Total $`,
	sub.`# Pledges`,
	sub.`Acquisition $`,
	sub.`Lapsed $`,
	sub.`Renewal $`,
	sub.`White Glove $`,
	sub.`Operathon $`,
	sub.`Prospect $`,
	sub.`Avg New Gift $`,
	sub.`Renewal $ inc/dec`,
	sub.`Renewal Response Rate`,
	sub.`Acq Response Rate`,
	sub.`2nd Appeal response rate`,
	sub.`Idle Time`,
	CONCAT(IFNULL(ROUND((sub.`Idle Time` / (sub.`Total Duration` / 3600)) * 100, 0),0),'%') as '% Idle Time',
	sub.`Training Time`,
	sub.`DNC %`,
	sub.`$/hour`,
	sub.`Credit card %`,
	TIME_FORMAT(SEC_TO_TIME(sub.`Total Duration`), '%H:%i:%s') as 'Total hours worked',
	sub.`Sch Hrs`,
	CONCAT(IFNULL(ROUND(((sub.`Sch Hrs` - (sub.`Total Duration` / 3600)) / sub.`Sch Hrs`) * 100, 0), 0),'%') as '% Missed Time',
	sub.`Dials/hour`,
	sub.`Avg Call duration`,
	sub.`% of goal attained`,
	sub.`total goal`
FROM
	(
	SELECT
		a.name AS 'Emp',
		CONCAT('$', FORMAT(IFNULL(SUM(CASE WHEN cr.paymentType = 'Credit Card' THEN cr.grandTotal ELSE 0 END), 0), 2)) AS 'CC $',
		CONCAT('$', FORMAT(CAST(SUM(GREATEST(CASE WHEN cr.paymentType = 'Invoice' THEN cr.giftAmount - (IFNULL(l.lyAmount, IFNULL(l.lap1Amount, 0))) ELSE 0 END, 0)) AS UNSIGNED), 2)) AS 'New $ (Invoice)',
		CONCAT('$', FORMAT(IFNULL(SUM(CASE WHEN cr.paymentType = 'Invoice' THEN (i.grandTotal - coalesce(i.amountRemaining, 0))ELSE 0 END), 0), 2)) AS 'Paid Invoice $',
		CONCAT('$', FORMAT(IFNULL(SUM(CASE WHEN cr.paymentType = 'Invoice' THEN (i.grandTotal - coalesce(i.amountRemaining, 0))ELSE cr.grandTotal END), 0), 2)) AS 'Total $',
		SUM(CASE WHEN cr.giftAmount > 0 THEN 1 ELSE 0 END) AS '# Pledges',
		CONCAT('$', FORMAT(CAST(SUM(GREATEST(CASE WHEN cr.giftamount > 0 THEN cr.giftAmount - (IFNULL(l.lyAmount, IFNULL(l.lap1Amount, 0))) ELSE 0 END, 0)) AS UNSIGNED), 2)) AS 'New $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN (cr.skill = 'Acquisition' OR cr.skill = 'Acquistion' OR cr.skill = 'Acquisition ' OR cr.skill = 'Aqcuisition') AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'Acquisition $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN cr.skill = 'Lapsed' AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'Lapsed $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN cr.skill = 'Renewal' AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'Renewal $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN cr.skill = 'White Glove' AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'White Glove $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN cr.skill = 'Operathon' AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'Operathon $',
		CONCAT('$', FORMAT(CAST(SUM(CASE WHEN cr.skill = 'Prospect' AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) AS UNSIGNED), 2)) AS 'Prospect $',
		CONCAT('$', FORMAT(IFNULL(CAST((SUM(CASE WHEN (cr.skill = 'Acquisition' OR cr.skill = 'Acquistion' OR cr.skill = 'Acquisition ' OR cr.skill = 'Aqcuisition') AND cr.giftamount > 0 THEN cr.giftAmount ELSE 0 END) / SUM(CASE WHEN (cr.skill = 'Acquisition' OR cr.skill = 'Acquistion' OR cr.skill = 'Acquisition ' OR cr.skill = 'Aqcuisition') AND cr.giftamount > 0 THEN 1 ELSE 0 END)) AS UNSIGNED), 0), 2)) AS 'Avg New Gift $',
		CONCAT('$', FORMAT(IFNULL(ROUND(SUM(CASE WHEN cr.skill = 'Renewal' AND cr.giftAmount > 0 THEN cr.giftAmount - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) ELSE 0 END)), 0), 2)) AS 'Renewal $ inc/dec',
		CONCAT(IFNULL(ROUND((SUM(CASE WHEN cr.skill = 'Renewal' AND cr.giftAmount > 0 THEN 1 ELSE 0 END) / SUM(CASE WHEN (cr.wrapup = 'Standard Refusal' OR cr.wrapup = 'Exception Refusal' OR cr.giftAmount > 0) THEN 1 ELSE 0 END)) * 100), 0), '%') AS 'Renewal Response Rate',
		CONCAT(IFNULL(ROUND((SUM(CASE WHEN (cr.skill = 'Acquisition' OR cr.skill = 'Acquistion' OR cr.skill = 'Acquisition ' OR cr.skill = 'Aqcuisition') AND cr.giftAmount > 0 THEN 1 ELSE 0 END) / SUM(CASE WHEN (cr.wrapup = 'Standard Refusal' OR cr.wrapup = 'Exception Refusal' OR cr.giftAmount > 0) THEN 1 ELSE 0 END)) * 100), 0), '%') AS 'Acq Response Rate',
		CONCAT(IFNULL(ROUND((SUM(CASE WHEN cr.subskill LIKE '2nd Appeal%' AND cr.giftAmount > 0 THEN 1 ELSE 0 END) / SUM(CASE WHEN (cr.wrapup = 'Standard Refusal' OR cr.wrapup = 'Exception Refusal' OR cr.giftAmount > 0) THEN 1 ELSE 0 END)) * 100), 0), '%') AS '2nd Appeal response rate',
		COALESCE(idle.idleTime,
	'00:00:00') as 'Idle Time',
		COALESCE(tr.trainingTime,
	'00:00:00') as 'Training Time',
		CONCAT(IFNULL(ROUND((SUM(CASE WHEN cr.refusalReason = 'Do not call again' THEN 1 ELSE 0 END) / SUM(CASE WHEN cr.refusalReason IS NOT NULL THEN 1 ELSE 0 END)) * 100), 0), '%') 'DNC %',
		CONCAT('$', FORMAT(IFNULL(ROUND(SUM(cr.giftAmount) / (SELECT IFNULL(ROUND(SUM(duration) / 60 / 60), 0) FROM userevents WHERE agentId = cr.agentId AND createdAt >= '{{startDate}}' AND createdAt <= '{{endDate}}')), 0), 2)) AS '$/hour',
		CONCAT(IFNULL(ROUND((SUM(CASE WHEN cr.wrapup = 'Pledge Credit Card' THEN 1 ELSE 0 END) / SUM(CASE WHEN cr.giftAmount > 0 THEN 1 ELSE 0 END)) * 100), 0), '%') AS 'Credit card %',
		(
		SELECT
			IFNULL(SUM(duration), 0)
		FROM
			userevents
		WHERE
			agentId = cr.agentId
			AND createdAt >= '{{startDate}}'
			AND createdAt <= '{{endDate}}'
		) AS 'Total Duration',
		(
		SELECT
			TIME_FORMAT(SEC_TO_TIME(IFNULL(SUM(duration), 0)), '%H:%i:%s')
		FROM
			userevents
		WHERE
			agentId = cr.agentId
			AND createdAt >= '{{startDate}}'
			AND createdAt <= '{{endDate}}') AS 'Total hours worked',
		a.id as 'Agent ID',
		COALESCE ((
		SELECT
			SUM(IFNULL(cat.scheduledHours, 0))
		FROM
			campaignagenttargets cat
		WHERE
			cat.agentId = a.id
			AND cat.start >= '{{startDate}}'
			AND cat.end <= '{{endDate}}'
	),
		0) AS 'Sch Hrs',
		IFNULL(ROUND(COUNT(cr.id) / (SELECT IFNULL(ROUND(SUM(duration) / 60 / 60), 0) FROM userevents WHERE agentId = cr.agentId AND createdAt >= '{{startDate}}' AND createdAt <= '{{endDate}}')), 0) AS 'Dials/hour',
		(
		SELECT
			TIME_FORMAT(SEC_TO_TIME(IFNULL(AVG(totalDurationSecs), 0)), '%H:%i:%s')
		FROM
			callrecords
		WHERE
			agentid = a.id
			AND createdat >= '{{startDate}}'
			AND createdat <= '{{endDate}}') AS 'Avg Call duration',
		CONCAT(IFNULL(ROUND(((SUM(cr.giftamount) / IFNULL((SELECT SUM(COALESCE(NULLIF(overrideGoal, 0), goal, 0)) FROM campaignagenttargets cat WHERE cat.start >= '{{startDate}}' AND cat.start <= '{{endDate}}' AND cat.agentid = a.id), 0)) * 100)), 0), '%') AS '% of goal attained',
		CONCAT('$',
	IFNULL((
	SELECT
		SUM(COALESCE(NULLIF(overrideGoal, 0), goal, 0))FROM campaignagenttargets cat WHERE cat.start >= '{{startDate}}' AND cat.start <= '{{endDate}}' AND cat.agentid = a.id),
	0)) AS 'total goal'
	FROM
		callresults cr
	LEFT JOIN invoices i ON
		cr.id = i.callresultid
	LEFT JOIN agents a ON
		cr.agentid = a.id
	LEFT JOIN leads l ON
		l.id = cr.leadid
	LEFT JOIN clients c on
		c.id = cr.clientId
	LEFT JOIN (
		SELECT
			ae.agentId,
			SEC_TO_TIME(SUM((SELECT UNIX_TIMESTAMP(ae2.createdat) - UNIX_TIMESTAMP(ae.createdat) FROM agentevents ae2 WHERE ae2.agentid = ae.agentid AND ae2.id > ae.id LIMIT 1))) AS trainingTime
		FROM
			agentevents ae
		WHERE
			ae.createdat >= '{{startDate}}'
			AND ae.createdat <= '{{endDate}}'
			AND ae.eventName = 'Training'
		GROUP BY
			ae.agentId) tr ON
		tr.agentId = cr.agentId
	LEFT JOIN (
		SELECT
			ae.agentId,
			SEC_TO_TIME(SUM((SELECT UNIX_TIMESTAMP(ae2.createdat) - UNIX_TIMESTAMP(ae.createdat) FROM agentevents ae2 WHERE ae2.agentid = ae.agentid AND ae2.id > ae.id LIMIT 1))) AS idleTime
		FROM
			agentevents ae
		WHERE
			ae.createdat >= '{{startDate}}'
			AND ae.createdat <= '{{endDate}}'
			AND ae.eventName = 'Idle'
		GROUP BY
			ae.agentId) idle ON
		idle.agentId = cr.agentId
	WHERE
		cr.agentid IS NOT NULL
		AND cr.createdAt >= '{{startDate}}'
		AND cr.createdAt <= '{{endDate}}'
		AND {{whereClause}}
	GROUP BY
		cr.agentid
	ORDER BY
		a.name
) as sub