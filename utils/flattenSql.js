#!/usr/bin/env node

// This line imports Node's built-in File System module.
var fs = require('fs');

/**
 * Flattens a multi-line SQL string by replacing newlines and tabs.
 *
 * @param {string} sqlString - The multi-line SQL query.
 * @returns {string} - A single-line string with escaped characters.
 */
function flattenSqlQuery(sqlString) {
  if (typeof sqlString !== 'string') {
    return '';
  }
  // Replace all newline characters with a literal '\n'
  var processedString = sqlString.replace(/\r?\n|\r/g, '\\n');
  // Replace all tab characters with a literal '\t'
  processedString = processedString.replace(/\t/g, '\\t');
  return processedString;
}

// Get the filename from the command-line arguments.
// process.argv[2] is the first argument after 'node' and the script name.
var inputFile = process.argv[2];

// Check if a filename was provided.
if (!inputFile) {
  console.error('Error: Please provide a filename as an argument.');
  console.error('Usage: node flattenSql.js <filename>');
  process.exit(1); // Exit with an error code.
}

// Read the contents of the specified file.
fs.readFile(inputFile, 'utf8', function(err, data) {
  if (err) {
    console.error('Error reading file:', err.message);
    process.exit(1);
  }

  // Process the file content with the function.
  var flattenedQuery = flattenSqlQuery(data);

  // Print the final result to the console.
  console.log(flattenedQuery);
});