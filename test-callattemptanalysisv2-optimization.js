/**
 * Test Script for Call Attempt Analysis V2 Optimization
 * 
 * This script helps verify that the optimized endpoint works correctly.
 * 
 * Usage:
 *   node test-callattemptanalysisv2-optimization.js <campaignId>
 * 
 * Example:
 *   node test-callattemptanalysisv2-optimization.js 123
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_URL || 'http://localhost:3000';
const API_PATH = '/api/campaigns';

// Get campaign ID from command line
const campaignId = process.argv[2];

if (!campaignId) {
    console.error('Error: Campaign ID is required');
    console.log('Usage: node test-callattemptanalysisv2-optimization.js <campaignId>');
    process.exit(1);
}

/**
 * Test the optimized endpoint
 */
async function testOptimizedEndpoint() {
    console.log('='.repeat(80));
    console.log('Testing Optimized Call Attempt Analysis V2 Endpoint');
    console.log('='.repeat(80));
    console.log(`Campaign ID: ${campaignId}`);
    console.log(`API URL: ${BASE_URL}${API_PATH}/${campaignId}/callattemptanalysisv2`);
    console.log('');

    try {
        // Test 1: Basic request without agent filtering
        console.log('Test 1: Basic Request (No Agent Filter)');
        console.log('-'.repeat(80));
        const startTime1 = Date.now();
        
        const response1 = await axios.post(
            `${BASE_URL}${API_PATH}/${campaignId}/callattemptanalysisv2`,
            {},
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const endTime1 = Date.now();
        const duration1 = endTime1 - startTime1;
        
        console.log(`✓ Request completed in ${duration1}ms`);
        console.log('');
        
        // Validate response structure
        validateResponse(response1.data);
        
        // Display summary
        displaySummary(response1.data);
        
        // Test 2: Request with agent filtering (if applicable)
        console.log('');
        console.log('Test 2: Request with Agent Filter');
        console.log('-'.repeat(80));
        
        const startTime2 = Date.now();
        
        const response2 = await axios.post(
            `${BASE_URL}${API_PATH}/${campaignId}/callattemptanalysisv2`,
            {
                agentId: 1 // Use a valid agent ID for your system
            },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const endTime2 = Date.now();
        const duration2 = endTime2 - startTime2;
        
        console.log(`✓ Request completed in ${duration2}ms`);
        console.log('');
        
        validateResponse(response2.data);
        displaySummary(response2.data);
        
        // Performance summary
        console.log('');
        console.log('='.repeat(80));
        console.log('Performance Summary');
        console.log('='.repeat(80));
        console.log(`Test 1 (No Filter): ${duration1}ms`);
        console.log(`Test 2 (With Filter): ${duration2}ms`);
        console.log('');
        
        if (duration1 < 30000) {
            console.log('✓ PASS: Response time is acceptable (< 30 seconds)');
        } else {
            console.log('⚠ WARNING: Response time is slow (> 30 seconds)');
        }
        
    } catch (error) {
        console.error('✗ Test Failed');
        console.error('Error:', error.message);
        
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        }
        
        process.exit(1);
    }
}

/**
 * Validate response structure
 */
function validateResponse(data) {
    console.log('Validating Response Structure...');
    
    const errors = [];
    
    // Check required top-level fields
    if (typeof data.totalLeads !== 'number') {
        errors.push('Missing or invalid totalLeads');
    }
    
    if (typeof data.totalViable !== 'number') {
        errors.push('Missing or invalid totalViable');
    }
    
    if (!data.totalDialAttemptBuckets) {
        errors.push('Missing totalDialAttemptBuckets');
    } else {
        // Validate bucket structure
        const buckets = ['zero', 'one', 'twoToFour', 'fiveToNineteen', 'twentyPlus'];
        buckets.forEach(bucket => {
            if (typeof data.totalDialAttemptBuckets[bucket] !== 'number') {
                errors.push(`Missing or invalid bucket: ${bucket}`);
            }
        });
        
        // Check that only zero bucket has values (per optimization)
        if (data.totalDialAttemptBuckets.one !== 0 ||
            data.totalDialAttemptBuckets.twoToFour !== 0 ||
            data.totalDialAttemptBuckets.fiveToNineteen !== 0 ||
            data.totalDialAttemptBuckets.twentyPlus !== 0) {
            console.log('⚠ Note: Non-zero buckets have values (expected 0 after optimization)');
        }
    }
    
    if (!Array.isArray(data.stages)) {
        errors.push('Missing or invalid stages array');
    }
    
    if (errors.length > 0) {
        console.log('✗ Validation Failed:');
        errors.forEach(err => console.log(`  - ${err}`));
        process.exit(1);
    } else {
        console.log('✓ Response structure is valid');
    }
}

/**
 * Display summary of results
 */
function displaySummary(data) {
    console.log('');
    console.log('Results Summary:');
    console.log('-'.repeat(80));
    console.log(`Total Leads: ${data.totalLeads}`);
    console.log(`Total Viable: ${data.totalViable}`);
    console.log(`Total Exhausted: ${data.totalExhausted || data.totalNoCallAttempts || 0}`);
    console.log(`Total Callbacks: ${data.totalCallback || 0}`);
    console.log(`Total Bad Numbers: ${data.totalBadNumbers || 0}`);
    console.log('');
    console.log('Dial Attempt Buckets:');
    console.log(`  Zero Dials: ${data.totalDialAttemptBuckets.zero}`);
    console.log(`  One Dial: ${data.totalDialAttemptBuckets.one}`);
    console.log(`  2-4 Dials: ${data.totalDialAttemptBuckets.twoToFour}`);
    console.log(`  5-19 Dials: ${data.totalDialAttemptBuckets.fiveToNineteen}`);
    console.log(`  20+ Dials: ${data.totalDialAttemptBuckets.twentyPlus}`);
    console.log('');
    console.log(`Number of Stages: ${data.stages ? data.stages.length : 0}`);
    
    if (data.stages && data.stages.length > 0) {
        console.log('');
        console.log('Stage Breakdown:');
        data.stages.forEach(stage => {
            console.log(`  Stage ${stage.id} (${stage.name}):`);
            console.log(`    Leads: ${stage.leads || 0}`);
            console.log(`    Viable: ${stage.viable || 0}`);
            console.log(`    Zero Dials: ${stage.dialAttemptBuckets ? stage.dialAttemptBuckets.zero : 0}`);
            console.log(`    Reporting Groups: ${stage.reportingGroups ? stage.reportingGroups.length : 0}`);
        });
    }
}

// Run the test
testOptimizedEndpoint()
    .then(() => {
        console.log('');
        console.log('='.repeat(80));
        console.log('✓ All Tests Passed');
        console.log('='.repeat(80));
        process.exit(0);
    })
    .catch(error => {
        console.error('Unexpected error:', error);
        process.exit(1);
    });

