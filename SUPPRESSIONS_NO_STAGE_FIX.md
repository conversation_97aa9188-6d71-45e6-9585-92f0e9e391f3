# Suppressions "No Stage" Fix - V2 Endpoint

## Problem Identified

The V2 `callattemptanalysisv2` endpoint was **not handling "No Stage" suppressions** the same way as the V1 `callattemptanalysis` endpoint.

### V1 Behavior (Original)
- **Query:** `SELECT campaignstageId as 'stage', tfSubSkillId as 'skillId', COUNT(*) as 'count'`
- **Logic:** 
  - If `row.stage` exists → suppression has a stage
  - If `row.stage` is NULL → suppression has "No Stage", add to `suppressedNoStage`
- **Response:** Includes both `suppressedTotal` and `suppressedNoStage` fields
- **totalNoStage Calculation:** `totalNoStage - suppressedNoStage` (excludes suppressed leads from "No Stage" count)

### V2 Behavior (Before Fix)
- **Query:** `SELECT s.campaignstageId as 'stageId', ... WITH ROLLUP`
- **Logic:** Only processed suppressions that had a stage (due to ROLLUP structure)
- **Problem:** Suppressions with `NULL campaignstageId` were included in grand total but not separately tracked
- **Response:** Only included `suppressedTotal`, missing `suppressedNoStage`

## Solution Implemented

### 1. Added "No Stage" Tracking Variable

**Location:** `controllers/campaign.js` line 3551

```javascript
var suppressedTotal = 0
var suppressedNoStage = 0  // Track "No Stage" suppressions like V1
```

### 2. Added "No Stage" Detection Logic

**Location:** `controllers/campaign.js` lines 3578-3582

```javascript
// Handle "No Stage" suppressions (like V1)
// These are suppressions where stageId is null but reportingGroupId/skillId are not null
if (!row.stageId && (row.reportingGroupId || row.skillId)) {
    suppressedNoStage += parseInt(row.suppressed || 0)
    return
}
```

### 3. Updated Response Object

**Location:** `controllers/campaign.js` lines 3725-3726

```javascript
totalNoStage: totalNoStage - suppressedNoStage,  // Subtract suppressedNoStage like V1
suppressedNoStage: suppressedNoStage,  // Add suppressedNoStage like V1
```

### 4. Updated Documentation

**Location:** `controllers/campaign.js` line 2874

Added `suppressedNoStage: 5,` to the example response structure.

## How It Works

### Suppressions Query (Unchanged)
```sql
SELECT
    s.campaignstageId as 'stageId',
    l.tfSkillId as 'reportingGroupId',
    l.tfsubskillid as 'skillId',
    COUNT(*) as 'suppressed'
FROM suppressions s
INNER JOIN leads l ON l.id = s.leadId
WHERE s.campaignId = :campaignId
    AND s.finished = FALSE
    AND s.actualStartDate IS NOT NULL
    AND l.tfsubskillid IS NOT NULL
GROUP BY campaignstageId, l.tfSkillId, l.tfsubskillid WITH ROLLUP
```

### Processing Logic (Updated)

The ROLLUP query produces different types of rows:

1. **Grand Total:** `{ stageId: null, reportingGroupId: null, skillId: null, suppressed: 45 }`
   - **Action:** Set `suppressedTotal = 45`

2. **Stage Totals:** `{ stageId: 1, reportingGroupId: null, skillId: null, suppressed: 25 }`
   - **Action:** Set `stage.suppressions = 25`

3. **Reporting Group Totals:** `{ stageId: 1, reportingGroupId: 101, skillId: null, suppressed: 15 }`
   - **Action:** Set `reportingGroup.suppressions = 15`

4. **Lead Type Details:** `{ stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 8 }`
   - **Action:** Set `leadType.suppressed = 8`

5. **"No Stage" Suppressions:** `{ stageId: null, reportingGroupId: 101, skillId: 201, suppressed: 3 }`
   - **Action:** Add to `suppressedNoStage += 3` ✅ **NEW**

## Response Structure

### Before Fix
```json
{
  "suppressedTotal": 45,
  "totalNoStage": 574
}
```

### After Fix
```json
{
  "suppressedTotal": 45,
  "suppressedNoStage": 5,
  "totalNoStage": 569
}
```

**Note:** `totalNoStage` is now calculated as `totalNoStage - suppressedNoStage` to match V1 behavior.

## Key Differences from V1

### V1 Implementation
- Uses simple GROUP BY without ROLLUP
- Processes each suppression row individually
- Checks `if (row.stage)` vs `else` for "No Stage"

### V2 Implementation (Fixed)
- Uses ROLLUP for hierarchical aggregation
- Processes ROLLUP levels systematically
- Checks `if (!row.stageId && (row.reportingGroupId || row.skillId))` for "No Stage"

## Testing

### Test Cases to Verify

1. **Campaign with no suppressions**
   - `suppressedTotal = 0`
   - `suppressedNoStage = 0`

2. **Campaign with only staged suppressions**
   - `suppressedTotal > 0`
   - `suppressedNoStage = 0`
   - Stage/reporting group/lead type suppressions populated

3. **Campaign with only "No Stage" suppressions**
   - `suppressedTotal > 0`
   - `suppressedNoStage = suppressedTotal`
   - No stage-level suppressions

4. **Campaign with mixed suppressions**
   - `suppressedTotal > suppressedNoStage`
   - Both staged and "No Stage" suppressions counted

### Test Query
```sql
-- Check for "No Stage" suppressions in a campaign
SELECT 
    s.campaignstageId,
    COUNT(*) as count
FROM suppressions s
INNER JOIN leads l ON l.id = s.leadId
WHERE s.campaignId = 123
    AND s.finished = FALSE
    AND s.actualStartDate IS NOT NULL
    AND l.tfsubskillid IS NOT NULL
GROUP BY s.campaignstageId
```

If this query returns rows where `campaignstageId IS NULL`, those should now be counted in `suppressedNoStage`.

## Validation

### V1 vs V2 Comparison
For the same campaign, both endpoints should now return:
- Same `suppressedTotal` value
- Same `suppressedNoStage` value  
- Same `totalNoStage` value (after subtracting suppressedNoStage)

### Frontend Impact
The frontend already expects both fields since V1 provides them:
- `suppressedTotal` - Total suppressions across all stages
- `suppressedNoStage` - Suppressions with no assigned stage

## Files Modified

- ✅ **controllers/campaign.js** - Added "No Stage" suppressions tracking to V2 endpoint

## Backward Compatibility

- ✅ **No breaking changes** - Only added missing functionality
- ✅ **Response structure enhanced** - Added `suppressedNoStage` field that was missing
- ✅ **V1 behavior preserved** - V1 endpoint unchanged
- ✅ **Frontend compatible** - Frontend already handles both fields

---

**Date:** 2025-10-03  
**Status:** Complete  
**Impact:** V2 endpoint now handles "No Stage" suppressions identically to V1
