# Suppressions "No Stage" Fix - V2 Endpoint

## Problem Identified

The V2 `callattemptanalysisv2` endpoint was **not handling "No Stage" suppressions** the same way as the V1 `callattemptanalysis` endpoint.

### V1 Behavior (Original)
- **Query:** `SELECT campaignstageId as 'stage', tfSubSkillId as 'skillId', COUNT(*) as 'count'`
- **Logic:** 
  - If `row.stage` exists → suppression has a stage
  - If `row.stage` is NULL → suppression has "No Stage", add to `suppressedNoStage`
- **Response:** Includes both `suppressedTotal` and `suppressedNoStage` fields
- **totalNoStage Calculation:** `totalNoStage - suppressedNoStage` (excludes suppressed leads from "No Stage" count)

### V2 Behavior (Before Fix)
- **Query:** `SELECT s.campaignstageId as 'stageId', ... WITH ROLLUP`
- **Logic:** Only processed suppressions that had a stage (due to ROLLUP structure)
- **Problem:** Suppressions with `NULL campaignstageId` were included in grand total but not separately tracked
- **Response:** Only included `suppressedTotal`, missing `suppressedNoStage`

## Solution Implemented

### 1. Removed WITH ROLLUP from Suppressions Query

**Location:** `controllers/campaign.js` line 2790

**Before:**
```sql
GROUP BY campaignstageId, l.tfSkillId, l.tfsubskillid WITH ROLLUP
```

**After:**
```sql
GROUP BY campaignstageId, l.tfSkillId, l.tfsubskillid
```

**Reason:** WITH ROLLUP creates intermediate rows that would be incorrectly counted as "No Stage" suppressions.

### 2. Added "No Stage" Tracking Variable

**Location:** `controllers/campaign.js` line 3551

```javascript
var suppressedTotal = 0
var suppressedNoStage = 0  // Track "No Stage" suppressions like V1
```

### 3. Implemented V1-Style Processing Logic

**Location:** `controllers/campaign.js` lines 3560-3610

```javascript
// Add to total count (like V1)
suppressedTotal += parseInt(row.suppressed || 0)

// Check if suppression has a stage (like V1)
if (row.stageId) {
    // Has stage - add to stage/reportingGroup/leadType totals
    // [Manual aggregation logic]
} else {
    // No stage - add to suppressedNoStage (like V1)
    suppressedNoStage += parseInt(row.suppressed || 0)
}
```

### 3. Updated Response Object

**Location:** `controllers/campaign.js` lines 3725-3726

```javascript
totalNoStage: totalNoStage - suppressedNoStage,  // Subtract suppressedNoStage like V1
suppressedNoStage: suppressedNoStage,  // Add suppressedNoStage like V1
```

### 4. Updated Documentation

**Location:** `controllers/campaign.js` line 2874

Added `suppressedNoStage: 5,` to the example response structure.

## How It Works

### Suppressions Query (Fixed)
```sql
SELECT
    s.campaignstageId as 'stageId',
    l.tfSkillId as 'reportingGroupId',
    l.tfsubskillid as 'skillId',
    COUNT(*) as 'suppressed'
FROM suppressions s
INNER JOIN leads l ON l.id = s.leadId
WHERE s.campaignId = :campaignId
    AND s.finished = FALSE
    AND s.actualStartDate IS NOT NULL
    AND l.tfsubskillid IS NOT NULL
GROUP BY campaignstageId, l.tfSkillId, l.tfsubskillid
```

**Key Change:** Removed `WITH ROLLUP` to match V1 approach and avoid ROLLUP intermediate rows being counted as "No Stage".

### Processing Logic (Fixed)

The non-ROLLUP query produces individual suppression rows like V1:

1. **Staged Suppression:** `{ stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 8 }`
   - **Action:**
     - Add to `suppressedTotal += 8`
     - Add to `stage.suppressions += 8`
     - Add to `reportingGroup.suppressions += 8`
     - Add to `leadType.suppressions += 8`

2. **"No Stage" Suppression:** `{ stageId: null, reportingGroupId: 101, skillId: 201, suppressed: 3 }`
   - **Action:**
     - Add to `suppressedTotal += 3`
     - Add to `suppressedNoStage += 3` ✅ **FIXED**

**Key Fix:** No more ROLLUP intermediate rows that could be mistakenly counted as "No Stage" suppressions.

## Response Structure

### Before Fix
```json
{
  "suppressedTotal": 45,
  "totalNoStage": 574
}
```

### After Fix
```json
{
  "suppressedTotal": 45,
  "suppressedNoStage": 5,
  "totalNoStage": 569
}
```

**Note:** `totalNoStage` is now calculated as `totalNoStage - suppressedNoStage` to match V1 behavior.

## Key Differences from V1

### V1 Implementation
- Uses simple GROUP BY without ROLLUP
- Processes each suppression row individually
- Checks `if (row.stage)` vs `else` for "No Stage"

### V2 Implementation (Fixed)
- Uses ROLLUP for hierarchical aggregation
- Processes ROLLUP levels systematically
- Checks `if (!row.stageId && (row.reportingGroupId || row.skillId))` for "No Stage"

## Testing

### Test Cases to Verify

1. **Campaign with no suppressions**
   - `suppressedTotal = 0`
   - `suppressedNoStage = 0`

2. **Campaign with only staged suppressions**
   - `suppressedTotal > 0`
   - `suppressedNoStage = 0`
   - Stage/reporting group/lead type suppressions populated

3. **Campaign with only "No Stage" suppressions**
   - `suppressedTotal > 0`
   - `suppressedNoStage = suppressedTotal`
   - No stage-level suppressions

4. **Campaign with mixed suppressions**
   - `suppressedTotal > suppressedNoStage`
   - Both staged and "No Stage" suppressions counted

### Test Query
```sql
-- Check for "No Stage" suppressions in a campaign
SELECT 
    s.campaignstageId,
    COUNT(*) as count
FROM suppressions s
INNER JOIN leads l ON l.id = s.leadId
WHERE s.campaignId = 123
    AND s.finished = FALSE
    AND s.actualStartDate IS NOT NULL
    AND l.tfsubskillid IS NOT NULL
GROUP BY s.campaignstageId
```

If this query returns rows where `campaignstageId IS NULL`, those should now be counted in `suppressedNoStage`.

## Validation

### V1 vs V2 Comparison
For the same campaign, both endpoints should now return:
- Same `suppressedTotal` value
- Same `suppressedNoStage` value  
- Same `totalNoStage` value (after subtracting suppressedNoStage)

### Frontend Impact
The frontend already expects both fields since V1 provides them:
- `suppressedTotal` - Total suppressions across all stages
- `suppressedNoStage` - Suppressions with no assigned stage

## Files Modified

- ✅ **controllers/campaign.js** - Added "No Stage" suppressions tracking to V2 endpoint

## Backward Compatibility

- ✅ **No breaking changes** - Only added missing functionality
- ✅ **Response structure enhanced** - Added `suppressedNoStage` field that was missing
- ✅ **V1 behavior preserved** - V1 endpoint unchanged
- ✅ **Frontend compatible** - Frontend already handles both fields

---

**Date:** 2025-10-03  
**Status:** Complete  
**Impact:** V2 endpoint now handles "No Stage" suppressions identically to V1
